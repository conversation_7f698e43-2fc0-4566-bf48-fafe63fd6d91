package com.totwoo.totwoo.tcp

import android.os.Handler
import android.os.Looper
import com.tencent.mars.xlog.Log
import java.io.*
import java.net.InetSocketAddress
import java.net.Socket
import java.net.SocketTimeoutException
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger
import kotlin.concurrent.thread

/**
 * TCP 长连接管理器
 * 支持自动重连、心跳保活、连接状态监听
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
class TcpConnectionManager private constructor() {
    
    companion object {
        private const val TAG = "TcpConnectionManager"
        private const val CONNECT_TIMEOUT = 10_000 // 10秒连接超时
        private const val READ_TIMEOUT = 35_000 // 35秒读取超时，比心跳间隔长
        private const val HEARTBEAT_INTERVAL = 30_000L // 30秒心跳间隔
        private const val RECONNECT_INTERVAL = 5_000L // 5秒重连间隔
        private const val MAX_RECONNECT_ATTEMPTS = 10 // 最大重连次数
        private const val HEARTBEAT_MESSAGE = "PING"
        private const val HEARTBEAT_RESPONSE = "PONG"
        
        @Volatile
        private var INSTANCE: TcpConnectionManager? = null
        
        fun getInstance(): TcpConnectionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TcpConnectionManager().also { INSTANCE = it }
            }
        }
    }
    
    // 连接相关
    private var socket: Socket? = null
    private var outputStream: PrintWriter? = null
    private var inputStream: BufferedReader? = null
    
    // 连接参数
    private var serverHost: String = ""
    private var serverPort: Int = 0
    
    // 状态控制
    private val isConnected = AtomicBoolean(false)
    private val isConnecting = AtomicBoolean(false)
    private val shouldReconnect = AtomicBoolean(true)
    private val reconnectAttempts = AtomicInteger(0)
    
    // 心跳相关
    private var lastHeartbeatTime = 0L
    private var heartbeatFailCount = 0
    private var isScreenOff = false
    
    // 线程和处理器
    private val mainHandler = Handler(Looper.getMainLooper())
    private var readThread: Thread? = null
    private var heartbeatRunnable: Runnable? = null
    private var reconnectRunnable: Runnable? = null
    
    // 监听器
    private var connectionListener: ConnectionListener? = null
    
    interface ConnectionListener {
        fun onConnected()
        fun onDisconnected(reason: String)
        fun onMessage(message: String)
        fun onError(error: String)
        fun onReconnecting(attempt: Int)
    }
    
    /**
     * 连接到TCP服务器
     */
    fun connect(host: String, port: Int, listener: ConnectionListener? = null) {
        Log.i(TAG, "准备连接 TCP 服务器: $host:$port")
        
        this.serverHost = host
        this.serverPort = port
        this.connectionListener = listener
        
        shouldReconnect.set(true)
        reconnectAttempts.set(0)
        
        connectInternal()
    }
    
    /**
     * 内部连接方法
     */
    private fun connectInternal() {
        if (isConnecting.get()) {
            Log.w(TAG, "TCP 正在连接中，跳过重复连接")
            return
        }
        
        if (isConnected.get()) {
            Log.i(TAG, "关闭现有 TCP 连接")
            disconnectInternal()
        }
        
        thread(name = "TcpConnect") {
            try {
                isConnecting.set(true)
                Log.i(TAG, "开始连接 TCP 服务器: $serverHost:$serverPort")
                
                // 创建Socket连接
                socket = Socket().apply {
                    soTimeout = READ_TIMEOUT
                    tcpNoDelay = true // 禁用Nagle算法，减少延迟
                    keepAlive = true // 启用TCP Keep-Alive
                }
                
                socket?.connect(InetSocketAddress(serverHost, serverPort), CONNECT_TIMEOUT)
                
                // 创建输入输出流
                outputStream = PrintWriter(
                    BufferedWriter(OutputStreamWriter(socket?.getOutputStream(), "UTF-8")),
                    true // 自动flush
                )
                inputStream = BufferedReader(
                    InputStreamReader(socket?.getInputStream(), "UTF-8")
                )
                
                isConnected.set(true)
                isConnecting.set(false)
                reconnectAttempts.set(0)
                
                Log.i(TAG, "✅ TCP 连接成功: $serverHost:$serverPort")
                
                // 通知连接成功
                mainHandler.post {
                    connectionListener?.onConnected()
                }
                
                // 启动心跳
                startHeartbeat()
                
                // 启动读取线程
                startReadThread()
                
            } catch (e: Exception) {
                isConnecting.set(false)
                isConnected.set(false)
                
                Log.e(TAG, "❌ TCP 连接失败: ${e.message}")
                
                mainHandler.post {
                    connectionListener?.onError("连接失败: ${e.message}")
                }
                
                // 尝试重连
                scheduleReconnect()
            }
        }
    }
    
    /**
     * 启动读取线程
     */
    private fun startReadThread() {
        readThread = thread(name = "TcpRead") {
            try {
                while (isConnected.get() && !Thread.currentThread().isInterrupted) {
                    val message = inputStream?.readLine()
                    if (message != null) {
                        Log.d(TAG, "📨 收到消息: $message")
                        
                        // 处理心跳响应
                        if (message == HEARTBEAT_RESPONSE) {
                            Log.d(TAG, "💓 收到心跳响应")
                            heartbeatFailCount = 0
                        } else {
                            // 通知收到消息
                            mainHandler.post {
                                connectionListener?.onMessage(message)
                            }
                        }
                    } else {
                        // 连接断开
                        Log.w(TAG, "❌ TCP 连接断开 (读取到null)")
                        handleDisconnection("服务器关闭连接")
                        break
                    }
                }
            } catch (e: SocketTimeoutException) {
                Log.w(TAG, "❌ TCP 读取超时")
                handleDisconnection("读取超时")
            } catch (e: IOException) {
                if (isConnected.get()) {
                    Log.e(TAG, "❌ TCP 读取异常: ${e.message}")
                    handleDisconnection("读取异常: ${e.message}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "❌ TCP 读取线程异常: ${e.message}")
                handleDisconnection("读取线程异常: ${e.message}")
            }
        }
    }
    
    /**
     * 处理连接断开
     */
    private fun handleDisconnection(reason: String) {
        if (!isConnected.get()) return
        
        Log.w(TAG, "❌ TCP 连接断开: $reason")
        
        isConnected.set(false)
        stopHeartbeat()
        
        mainHandler.post {
            connectionListener?.onDisconnected(reason)
        }
        
        // 清理资源
        closeSocket()
        
        // 尝试重连
        if (shouldReconnect.get()) {
            scheduleReconnect()
        }
    }
    
    /**
     * 发送消息
     */
    fun sendMessage(message: String): Boolean {
        return if (isConnected.get() && outputStream != null) {
            try {
                outputStream?.println(message)
                Log.d(TAG, "📤 发送消息: $message")
                true
            } catch (e: Exception) {
                Log.e(TAG, "❌ 发送消息失败: ${e.message}")
                handleDisconnection("发送消息失败: ${e.message}")
                false
            }
        } else {
            Log.w(TAG, "❌ TCP 未连接，无法发送消息: $message")
            false
        }
    }
    
    /**
     * 发送心跳
     */
    private fun sendHeartbeat(): Boolean {
        val currentTime = System.currentTimeMillis()
        val success = sendMessage(HEARTBEAT_MESSAGE)
        
        if (success) {
            lastHeartbeatTime = currentTime
            Log.d(TAG, "💓 发送心跳成功 ${if (isScreenOff) "(息屏)" else "(亮屏)"}")
        } else {
            heartbeatFailCount++
            Log.w(TAG, "💔 发送心跳失败，失败次数: $heartbeatFailCount")
        }
        
        return success
    }
    
    /**
     * 启动心跳
     */
    private fun startHeartbeat() {
        stopHeartbeat()
        
        heartbeatRunnable = object : Runnable {
            override fun run() {
                if (isConnected.get()) {
                    if (sendHeartbeat()) {
                        heartbeatFailCount = 0
                        // 根据息屏状态调整心跳间隔
                        val interval = if (isScreenOff) HEARTBEAT_INTERVAL * 2 else HEARTBEAT_INTERVAL
                        mainHandler.postDelayed(this, interval)
                    } else {
                        // 心跳失败，连续3次失败则重连
                        if (heartbeatFailCount >= 3) {
                            Log.w(TAG, "💔 连续心跳失败，主动重连")
                            handleDisconnection("心跳失败")
                        } else {
                            // 缩短重试间隔
                            mainHandler.postDelayed(this, 5000)
                        }
                    }
                }
            }
        }
        
        mainHandler.postDelayed(heartbeatRunnable!!, HEARTBEAT_INTERVAL)
        Log.d(TAG, "💓 开始心跳检测")
    }
    
    /**
     * 停止心跳
     */
    private fun stopHeartbeat() {
        heartbeatRunnable?.let {
            mainHandler.removeCallbacks(it)
            heartbeatRunnable = null
        }
        Log.d(TAG, "💔 停止心跳检测")
    }
    
    /**
     * 安排重连
     */
    private fun scheduleReconnect() {
        if (!shouldReconnect.get() || reconnectAttempts.get() >= MAX_RECONNECT_ATTEMPTS) {
            Log.w(TAG, "🛑 停止重连: shouldReconnect=${shouldReconnect.get()}, attempts=${reconnectAttempts.get()}")
            return
        }
        
        val attempt = reconnectAttempts.incrementAndGet()
        Log.i(TAG, "🔄 安排重连，第 $attempt 次，${RECONNECT_INTERVAL}ms 后执行")
        
        mainHandler.post {
            connectionListener?.onReconnecting(attempt)
        }
        
        reconnectRunnable = Runnable {
            connectInternal()
        }
        
        mainHandler.postDelayed(reconnectRunnable!!, RECONNECT_INTERVAL)
    }
    
    /**
     * 设置息屏状态
     */
    fun setScreenState(isScreenOff: Boolean) {
        this.isScreenOff = isScreenOff
        Log.i(TAG, "📱 屏幕状态变化: ${if (isScreenOff) "息屏" else "亮屏"}")
        
        // 息屏时立即发送一次心跳，确认连接状态
        if (isScreenOff && isConnected.get()) {
            sendHeartbeat()
        }
    }
    
    /**
     * 手动断开连接
     */
    fun disconnect() {
        Log.i(TAG, "🔌 手动断开 TCP 连接")
        
        shouldReconnect.set(false)
        disconnectInternal()
    }
    
    /**
     * 内部断开连接
     */
    private fun disconnectInternal() {
        isConnected.set(false)
        isConnecting.set(false)
        
        // 停止心跳和重连
        stopHeartbeat()
        reconnectRunnable?.let {
            mainHandler.removeCallbacks(it)
            reconnectRunnable = null
        }
        
        // 停止读取线程
        readThread?.interrupt()
        readThread = null
        
        // 关闭Socket
        closeSocket()
    }
    
    /**
     * 关闭Socket和流
     */
    private fun closeSocket() {
        try {
            outputStream?.close()
            inputStream?.close()
            socket?.close()
        } catch (e: Exception) {
            Log.w(TAG, "关闭Socket异常: ${e.message}")
        } finally {
            outputStream = null
            inputStream = null
            socket = null
        }
    }
    
    /**
     * 获取连接状态
     */
    fun isConnected(): Boolean = isConnected.get()
    
    /**
     * 获取连接状态描述
     */
    fun getConnectionStatus(): String {
        val baseStatus = when {
            isConnecting.get() -> "连接中..."
            isConnected.get() -> "已连接"
            !shouldReconnect.get() -> "已断开"
            reconnectAttempts.get() > 0 -> "重连中... (${reconnectAttempts.get()}/${MAX_RECONNECT_ATTEMPTS})"
            else -> "未连接"
        }
        
        val additionalInfo = mutableListOf<String>()
        if (isScreenOff) additionalInfo.add("息屏")
        if (heartbeatFailCount > 0) additionalInfo.add("心跳失败${heartbeatFailCount}次")
        if (lastHeartbeatTime > 0) {
            val timeSinceLastHeartbeat = (System.currentTimeMillis() - lastHeartbeatTime) / 1000
            additionalInfo.add("上次心跳${timeSinceLastHeartbeat}秒前")
        }
        
        return if (additionalInfo.isNotEmpty()) {
            "$baseStatus (${additionalInfo.joinToString(", ")})"
        } else {
            baseStatus
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        Log.i(TAG, "🧹 清理 TCP 资源")
        disconnect()
        connectionListener = null
    }
}
