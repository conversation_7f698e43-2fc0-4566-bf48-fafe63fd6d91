package com.totwoo.totwoo.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.companion.AssociationRequest
import android.companion.BluetoothDeviceFilter
import android.companion.CompanionDeviceManager
import android.content.Context
import android.content.IntentSender
import android.os.Build
import android.os.Bundle
import android.text.method.ScrollingMovementMethod
import androidx.annotation.RequiresApi
import com.totwoo.library.util.LogUtils
import com.totwoo.library.util.ext.click
import com.totwoo.totwoo.R
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.databinding.ActivityDebugBinding
import com.totwoo.totwoo.keepalive.companion.CompanionDeviceHelper
import com.totwoo.totwoo.utils.CommonUtils
import com.totwoo.totwoo.utils.PreferencesUtils
import com.totwoo.totwoo.websocket.WebSocketManager
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import android.os.Handler
import android.os.Looper
import android.content.BroadcastReceiver
import android.content.Intent
import android.content.IntentFilter
import android.content.ClipData
import android.content.ClipboardManager
import android.widget.Toast
import com.totwoo.totwoo.receiver.JpushReceiver

/**
 * @des: 调试页面，包含 CompanionDeviceHelper 调试功能
 * <AUTHOR>
 * @date 2024/12/20 11:42
 */
@RequiresApi(Build.VERSION_CODES.O)
class DebugActivity : BaseActivity() {
    private var binding: ActivityDebugBinding? = null
    private val dateFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    // WebSocket 相关
    private val webSocketManager = WebSocketManager.getInstance()
    private val webSocketUrl = "ws://************:8060/ws/xiaozhi/v1/"

    // 后台测试相关
    private var backgroundTestHandler: Handler? = null
    private var isBackgroundTesting = false
    private var backgroundTestStartTime = 0L

    // 息屏监听
    private var screenReceiver: BroadcastReceiver? = null

    private val deviceManager: CompanionDeviceManager by lazy {
        getSystemService(Context.COMPANION_DEVICE_SERVICE) as CompanionDeviceManager
    }


    @RequiresApi(Build.VERSION_CODES.O)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityDebugBinding.inflate(layoutInflater)
        setContentView(binding?.root)

        initViews()
        initCompanionDeviceHelper()
        initWebSocketMonitoring()
        initScreenStateMonitor()

//        autoPaired()

    }

    @RequiresApi(Build.VERSION_CODES.O)
    private fun autoPaired() {
        val deviceMac = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "")

        val deviceFilter: BluetoothDeviceFilter = BluetoothDeviceFilter.Builder()
    //            .setNamePattern(Pattern.compile("My device"))
    //            .addServiceUuid(ParcelUuid(UUID(0x123abcL, -1L)), null)
//            .setAddress("F1:CC:69:60:A2:6F")
            .build()

        // The argument provided in setSingleDevice() determines whether a single
        // device name or a list of them appears.
        val pairingRequest: AssociationRequest = AssociationRequest.Builder()
            .addDeviceFilter(deviceFilter)
            .setSingleDevice(false)
            .build()

        // When the app tries to pair with a Bluetooth device, show the
        // corresponding dialog box to the user.
        deviceManager.associate(
            pairingRequest,
            object : CompanionDeviceManager.Callback() {

                override fun onDeviceFound(chooserLauncher: IntentSender) {
                    startIntentSenderForResult(
                        chooserLauncher,
                        100, null, 0, 0, 0
                    )
                }

                override fun onFailure(error: CharSequence?) {
                    // Handle the failure.
                }
            }, null
        )
    }

    @SuppressLint("MissingPermission")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        when (requestCode) {
            100 -> when(resultCode) {
                Activity.RESULT_OK -> {
                    // The user chose to pair the app with a Bluetooth device.
                    val deviceToPair: BluetoothDevice? =
                        data?.getParcelableExtra(CompanionDeviceManager.EXTRA_DEVICE)
                    deviceToPair?.let { device ->
                        device.createBond()
                        // Maintain continuous interaction with a paired device.
                    }
                }
            }
            else -> super.onActivityResult(requestCode, resultCode, data)
        }
    }

    private fun initViews() {
        binding?.apply {
            // 设置日志文本框可滚动
            tvDebugLog.movementMethod = ScrollingMovementMethod()

            // 初始化 rid 显示
            initRidDisplay()

            // 原有的分享功能
            btnShare.click {
                CommonUtils.shareXLog(this@DebugActivity)
            }

            // CompanionDeviceHelper 调试按钮
            btnAutoAssociate.click {
                testAutoAssociate()
            }

            btnRequestNotificationAccess.click {
                testRequestNotificationAccess()
            }

            btnStartObserving.click {
                testStartObserving()
            }

            btnStopObserving.click {
                testStopObserving()
            }

            btnDisassociate.click {
                testDisassociate()
            }

            btnCheckStatus.click {
                checkAssociationStatus()
            }

            // WebSocket 测试按钮
            btnCheckDeviceId.click {
                checkDeviceId()
            }

            btnConnectWebSocket.click {
                connectWebSocket()
            }

            btnDisconnectWebSocket.click {
                disconnectWebSocket()
            }

            btnSendMessage.click {
                sendTestMessage()
            }

            btnCheckConnectionStatus.click {
                checkWebSocketStatus()
            }

            btnStartBackgroundTest.click {
                startBackgroundTest()
            }

            btnStopBackgroundTest.click {
                stopBackgroundTest()
            }
        }
    }

    /**
     * 初始化 rid 显示
     */
    private fun initRidDisplay() {
        val rid = PreferencesUtils.getString(this, JpushReceiver.REGISTER_ID, "")
        binding?.tvText?.apply {
            text = "rid：$rid"

            // 设置点击事件，支持复制
            click {
                if (rid.isNotEmpty()) {
                    val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val clip = ClipData.newPlainText("rid", rid)
                    clipboard.setPrimaryClip(clip)
                    Toast.makeText(this@DebugActivity, "rid 已复制到剪贴板", Toast.LENGTH_SHORT).show()
                    appendLog("📋 rid 已复制: $rid")
                } else {
                    Toast.makeText(this@DebugActivity, "rid 为空，无法复制", Toast.LENGTH_SHORT).show()
                    appendLog("❌ rid 为空，无法复制")
                }
            }
        }

        // 在日志中显示 rid 信息
        if (rid.isNotEmpty()) {
            appendLog("📱 当前 rid: $rid")
        } else {
            appendLog("⚠️ rid 未获取到，可能需要重新注册推送")
        }
    }

    private fun initCompanionDeviceHelper() {
        try {
            CompanionDeviceHelper.init()
            appendLog("✅ CompanionDeviceHelper 初始化成功")
        } catch (e: Exception) {
            appendLog("❌ CompanionDeviceHelper 初始化失败: ${e.message}")
        }
    }

    /**
     * 初始化 WebSocket 监控
     */
    private fun initWebSocketMonitoring() {
        val deviceId = "b8:f8:62:e3:2a:30"
        appendLog("🔧 WebSocket 长连接测试模块已初始化")
        appendLog("📡 目标服务器: $webSocketUrl")
        appendLog("📱 当前设备ID: $deviceId")
        appendLog("💡 提示: 可以测试息屏后台连接稳定性")

        // 记录初始化时间，用于测试长连接持续时间
        val initTime = dateFormat.format(Date())
        appendLog("⏰ 初始化时间: $initTime")
    }

    /**
     * 初始化息屏状态监听
     */
    private fun initScreenStateMonitor() {
        screenReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    Intent.ACTION_SCREEN_OFF -> {
                        appendLog("📱 屏幕息屏 - 开始测试后台连接稳定性")
                        webSocketManager.setScreenState(true)
                    }
                    Intent.ACTION_SCREEN_ON -> {
                        appendLog("📱 屏幕亮屏 - 恢复正常心跳频率")
                        webSocketManager.setScreenState(false)
                    }
                }
            }
        }

        val filter = IntentFilter().apply {
            addAction(Intent.ACTION_SCREEN_OFF)
            addAction(Intent.ACTION_SCREEN_ON)
        }

        registerReceiver(screenReceiver, filter)
        appendLog("🔧 息屏状态监听已启动")
    }



    /**
     * 测试自动关联已配对设备
     */
    private fun testAutoAssociate() {

//        autoPaired()
//        appendLog("🔄 开始测试自动关联已配对设备...")
//
        // 获取当前配对的设备信息
        val deviceMac = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "")
        val deviceName = PreferencesUtils.getString(this, BleParams.PAIRED_JEWELRY_NAME_TAG, "")

        if (deviceMac.isEmpty()) {
            appendLog("❌ 未找到已配对的设备MAC地址")
            return
        }

        appendLog("📱 设备信息: $deviceName ($deviceMac)")

        CompanionDeviceHelper.autoAssociateWithPairedDevice(
            activity = this,
            deviceMac = deviceMac,
            callback = object : CompanionDeviceHelper.CompanionDeviceCallback {
                override fun onDeviceAssociated(deviceMac: String) {
                    appendLog("✅ 设备关联成功: $deviceMac")
                }

                override fun onError(error: String) {
                    appendLog("❌ 设备关联失败: $error")
                }
            }
        )
    }

    /**
     * 测试申请后台运行权限
     */
    private fun testRequestNotificationAccess() {
        appendLog("🔄 开始测试申请后台运行权限...")

        try {
            CompanionDeviceHelper.requestNotificationAccess(this)
            appendLog("✅ 后台运行权限申请已发起")
        } catch (e: Exception) {
            appendLog("❌ 申请后台运行权限失败: ${e.message}")
        }
    }

    /**
     * 测试开始设备存在监听
     */
    private fun testStartObserving() {
        appendLog("🔄 开始测试设备存在监听...")

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            appendLog("❌ 设备监听功能需要 Android 12+ (当前: Android ${Build.VERSION.SDK_INT})")
            return
        }

        try {
            CompanionDeviceHelper.startObservingDevicePresence()
        } catch (e: Exception) {
            appendLog("❌ 启动设备监听失败: ${e.message}")
        }
    }

    /**
     * 测试停止设备监听
     */
    private fun testStopObserving() {

        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.S) {
            appendLog("❌ 设备监听功能需要 Android 12+ (当前: Android ${Build.VERSION.SDK_INT})")
            return
        }

        try {
            CompanionDeviceHelper.stopObservingDevicePresence()
        } catch (e: Exception) {
            appendLog("❌ 停止设备监听失败: ${e.message}")
        }
    }

    /**
     * 测试解除设备关联
     */
    private fun testDisassociate() {

        try {
            CompanionDeviceHelper.disassociateDevice()
        } catch (e: Exception) {
            appendLog("❌ 解除设备关联失败: ${e.message}")
        }
    }

    /**
     * 检查关联状态
     */
    private fun checkAssociationStatus() {
        appendLog("🔄 检查设备关联状态...")

        try {
            val isSupported = CompanionDeviceHelper.isCompanionDeviceSupported()
            appendLog("📋 系统支持状态: ${if (isSupported) "支持" else "不支持"} (需要 Android 8+)")
            appendLog("📋 当前 Android 版本: API ${Build.VERSION.SDK_INT} (Android ${getAndroidVersionName()})")

            if (isSupported) {
                val deviceMac = PreferencesUtils.getString(this, BleParams.PAIRED_BLE_ADRESS_TAG, "")

                appendLog("📋 设备MAC地址: $deviceMac")
                if (deviceMac.isNotEmpty()) {
                    // 检查特定设备是否已关联
                    val isSpecificDeviceAssociated = CompanionDeviceHelper.isDeviceAssociated(deviceMac)
                    appendLog("📋 当前设备是否已关联: ${if (isSpecificDeviceAssociated) "是" else "否"}")
                } else {
                    appendLog("❌ 没有可关联的设备")
                }
            }
        } catch (e: Exception) {
            appendLog("❌ 检查状态失败: ${e.message}")
        }
    }

    /**
     * 获取Android版本名称
     */
    private fun getAndroidVersionName(): String {
        return when (Build.VERSION.SDK_INT) {
            26 -> "8.0"
            27 -> "8.1"
            28 -> "9"
            29 -> "10"
            30 -> "11"
            31 -> "12"
            32 -> "12L"
            33 -> "13"
            34 -> "14"
            35 -> "15"
            else -> "Unknown"
        }
    }

    // ==================== WebSocket 测试方法 ====================

    /**
     * 检查设备ID
     */
    private fun checkDeviceId() {
        val deviceId = "b8:f8:62:e3:2a:30"

        appendLog("📱 设备ID检查结果:")
        appendLog("  ✓ 当前使用的设备ID: $deviceId")

    }

    /**
     * 连接 WebSocket
     */
    private fun connectWebSocket() {
        disconnectWebSocket()
        val deviceId = "b8:f8:62:e3:2a:30"
        appendLog("🔄 开始连接 WebSocket: $webSocketUrl")
        appendLog("📱 使用设备ID: $deviceId")

        webSocketManager.connect(webSocketUrl, deviceId, object : WebSocketManager.ConnectionListener {
            override fun onConnected() {
                appendLog("✅ WebSocket 连接成功")
            }

            override fun onDisconnected(reason: String) {
                appendLog("❌ WebSocket 连接断开: $reason")
            }

            override fun onMessage(text: String) {
                appendLog("📨 收到消息: $text")
            }

            override fun onError(error: String) {
                appendLog("❌ WebSocket 错误: $error")
            }

            override fun onReconnecting(attempt: Int) {
                appendLog("🔄 WebSocket 重连中... 第 $attempt 次")
            }
        })
    }

    /**
     * 断开 WebSocket
     */
    private fun disconnectWebSocket() {
        appendLog("🔌 手动断开 WebSocket 连接")
        webSocketManager.disconnect()
    }

    /**
     * 发送测试消息
     */
    private fun sendTestMessage() {
        val testMessage = """{"type":"test","message":"Hello from ToTwoo Debug","timestamp":${System.currentTimeMillis()}}"""

        if (webSocketManager.sendMessage(testMessage)) {
            appendLog("📤 发送测试消息成功: $testMessage")
        } else {
            appendLog("❌ 发送测试消息失败，WebSocket 未连接")
        }
    }

    /**
     * 检查 WebSocket 连接状态
     */
    private fun checkWebSocketStatus() {
        val status = webSocketManager.getConnectionStatus()
        val isConnected = webSocketManager.isConnected()

        appendLog("📋 WebSocket 状态: $status")
        appendLog("📋 是否已连接: ${if (isConnected) "是" else "否"}")
        appendLog("📋 服务器地址: $webSocketUrl")

        // 如果正在后台测试，显示测试时长
        if (isBackgroundTesting) {
            val testDuration = (System.currentTimeMillis() - backgroundTestStartTime) / 1000
            appendLog("📋 后台测试时长: ${testDuration}秒")
        }
    }

    /**
     * 开始后台保活测试
     */
    private fun startBackgroundTest() {
        if (isBackgroundTesting) {
            appendLog("⚠️ 后台测试已在进行中")
            return
        }

        appendLog("🚀 开始后台保活测试")
        appendLog("💡 建议: 现在可以息屏或切换到后台，观察连接稳定性")

        isBackgroundTesting = true
        backgroundTestStartTime = System.currentTimeMillis()

        // 如果 WebSocket 未连接，先连接
        if (!webSocketManager.isConnected()) {
            appendLog("🔄 WebSocket 未连接，先建立连接...")
            connectWebSocket()
        }

        // 启动后台测试定时器
        backgroundTestHandler = Handler(Looper.getMainLooper())
        startBackgroundTestTimer()
    }

    /**
     * 停止后台保活测试
     */
    private fun stopBackgroundTest() {
        if (!isBackgroundTesting) {
            appendLog("⚠️ 后台测试未在进行中")
            return
        }

        val testDuration = (System.currentTimeMillis() - backgroundTestStartTime) / 1000
        appendLog("🛑 停止后台保活测试")
        appendLog("📊 测试总时长: ${testDuration}秒 (${testDuration / 60}分${testDuration % 60}秒)")

        isBackgroundTesting = false
        backgroundTestHandler?.removeCallbacksAndMessages(null)
        backgroundTestHandler = null
    }

    /**
     * 后台测试定时器
     */
    private fun startBackgroundTestTimer() {
        val testRunnable = object : Runnable {
            override fun run() {
                if (!isBackgroundTesting) return

                val testDuration = (System.currentTimeMillis() - backgroundTestStartTime) / 1000
                val isConnected = webSocketManager.isConnected()

                // 每分钟记录一次状态
                if (testDuration % 60 == 0L) {
                    appendLog("⏰ 后台测试 ${testDuration / 60}分钟 - 连接状态: ${if (isConnected) "正常" else "断开"}")

                    // 发送测试消息验证连接
                    if (isConnected) {
                        val testMsg = """{"type":"background_test","duration":${testDuration},"timestamp":${System.currentTimeMillis()}}"""
                        webSocketManager.sendMessage(testMsg)
                    }
                }

                // 每10秒检查一次连接状态
                backgroundTestHandler?.postDelayed(this, 10_000)
            }
        }

        backgroundTestHandler?.post(testRunnable)
    }

    /**
     * 添加日志到界面
     */
    private fun appendLog(message: String) {
        val timestamp = dateFormat.format(Date())
        val logMessage = "[$timestamp] $message"

        LogUtils.d("DebugActivity", logMessage)

        runOnUiThread {
            binding?.tvDebugLog?.apply {
                val currentText = text.toString()
                val newText = if (currentText == "调试日志将显示在这里...") {
                    logMessage
                } else {
                    "$currentText\n$logMessage"
                }
                text = newText

                // 滚动到底部
                post {
                    val scrollAmount = layout?.getLineTop(lineCount) ?: 0
                    if (scrollAmount > height) {
                        scrollTo(0, scrollAmount - height)
                    }
                }
            }
        }
    }

    override fun initTopBar() {
        super.initTopBar()
        setTopBackIcon(R.drawable.back_icon_black)
        setTopTitle("调试")
        setTopLeftOnclik { finish() }
        setSpinState(false)
    }



    override fun onDestroy() {
        super.onDestroy()

        // 停止后台测试
        if (isBackgroundTesting) {
            stopBackgroundTest()
        }

        // 清理息屏监听
        screenReceiver?.let {
            unregisterReceiver(it)
            screenReceiver = null
        }

        // 清理 CompanionDeviceHelper 回调，防止内存泄露
        CompanionDeviceHelper.clearCurrentCallback()

        // 清理 WebSocket 资源
        webSocketManager.cleanup()

        binding = null
    }
}