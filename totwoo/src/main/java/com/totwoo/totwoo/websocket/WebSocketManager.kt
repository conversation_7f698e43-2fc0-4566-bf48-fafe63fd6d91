package com.totwoo.totwoo.websocket

import android.os.Handler
import android.os.Looper
import com.tencent.mars.xlog.Log
import okhttp3.*
import okio.ByteString
import java.util.concurrent.TimeUnit

/**
 * WebSocket 长连接管理器
 * 用于测试长连接稳定性，监听连接状态变化
 * 
 * <AUTHOR>
 * @date 2024/12/20
 */
class WebSocketManager private constructor() {
    
    companion object {
        private const val TAG = "WebSocketManager"
        private const val HEARTBEAT_INTERVAL = 25_000L // 25秒心跳，避免20秒ping超时
        private const val RECONNECT_INTERVAL = 3_000L // 3秒重连间隔，更快重连
        private const val MAX_RECONNECT_ATTEMPTS = 20 // 增加重连次数
        private const val PING_INTERVAL = 15_000L // 15秒ping间隔，更频繁检测
        
        @Volatile
        private var INSTANCE: WebSocketManager? = null
        
        fun getInstance(): WebSocketManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: WebSocketManager().also { INSTANCE = it }
            }
        }
    }
    
    private var webSocket: WebSocket? = null
    private var okHttpClient: OkHttpClient? = null
    private var request: Request? = null
    private var isConnecting = false
    private var isManualDisconnect = false
    private var reconnectAttempts = 0
    private var lastHeartbeatTime = 0L // 最后心跳时间
    private var heartbeatFailCount = 0 // 心跳失败计数
    private var isScreenOff = false // 息屏状态
    
    // 心跳和重连处理器
    private val mainHandler = Handler(Looper.getMainLooper())
    private val heartbeatRunnable = object : Runnable {
        override fun run() {
            if (sendHeartbeat()) {
                heartbeatFailCount = 0
                // 根据息屏状态调整心跳间隔
                val interval = if (isScreenOff) HEARTBEAT_INTERVAL * 2 else HEARTBEAT_INTERVAL
                mainHandler.postDelayed(this, interval)
            } else {
                heartbeatFailCount++
                Log.w(TAG, "💔 心跳发送失败，失败次数: $heartbeatFailCount")

                // 连续3次心跳失败，主动重连
                if (heartbeatFailCount >= 3) {
                    Log.w(TAG, "💔 连续心跳失败，主动重连")
                    reconnectDueToHeartbeatFailure()
                } else {
                    // 缩短重试间隔
                    mainHandler.postDelayed(this, 5000)
                }
            }
        }
    }
    
    private val reconnectRunnable = object : Runnable {
        override fun run() {
            if (!isManualDisconnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                Log.i(TAG, "尝试重连 WebSocket，第 ${reconnectAttempts + 1} 次")
                connectInternal()
            }
        }
    }
    
    // 连接状态监听器
    private var connectionListener: ConnectionListener? = null
    
    interface ConnectionListener {
        fun onConnected()
        fun onDisconnected(reason: String)
        fun onMessage(text: String)
        fun onError(error: String)
        fun onReconnecting(attempt: Int)
    }
    
    /**
     * 初始化 WebSocket 客户端 - 优化网络参数
     */
    private fun initOkHttpClient() {
        if (okHttpClient == null) {
            okHttpClient = OkHttpClient.Builder()
                .connectTimeout(15, TimeUnit.SECONDS) // 增加连接超时
                .readTimeout(60, TimeUnit.SECONDS) // 增加读取超时
                .writeTimeout(15, TimeUnit.SECONDS) // 增加写入超时
                .pingInterval(PING_INTERVAL, TimeUnit.MILLISECONDS) // 15秒ping间隔
                .retryOnConnectionFailure(true)
                .callTimeout(0, TimeUnit.SECONDS) // 禁用调用超时，保持长连接
                .build()

            Log.i(TAG, "🔧 OkHttp客户端配置:")
            Log.i(TAG, "  • 连接超时: 15秒")
            Log.i(TAG, "  • 读取超时: 60秒")
            Log.i(TAG, "  • Ping间隔: ${PING_INTERVAL/1000}秒")
        }
    }
    
    /**
     * 连接 WebSocket
     */
    fun connect(url: String, deviceId: String? = null, listener: ConnectionListener? = null) {
        Log.i(TAG, "准备连接 WebSocket: $url")
        Log.i(TAG, "设备ID: ${deviceId ?: "未提供"}")

        this.connectionListener = listener
        isManualDisconnect = false
        reconnectAttempts = 0

        initOkHttpClient()

        val requestBuilder = Request.Builder()
            .url(url)
            .addHeader("User-Agent", "ToTwoo-Android-Debug")

        // 添加设备ID头部
        if (!deviceId.isNullOrEmpty()) {
            requestBuilder.addHeader("device-id", deviceId)
            Log.i(TAG, "✅ 已添加设备ID头部: device-id = $deviceId")
        } else {
            Log.w(TAG, "⚠️ 警告: 设备ID为空，服务器可能会拒绝连接")
        }

        request = requestBuilder.build()

        // 记录请求头信息
        Log.i(TAG, "📋 WebSocket 请求头:")
        request?.headers?.let { headers ->
            for (i in 0 until headers.size) {
                Log.i(TAG, "  ${headers.name(i)}: ${headers.value(i)}")
            }
        }

        connectInternal()
    }
    
    /**
     * 内部连接方法
     */
    private fun connectInternal() {
        if (isConnecting) {
            Log.w(TAG, "WebSocket 正在连接中，跳过重复连接")
            return
        }
        
        if (webSocket != null) {
            Log.i(TAG, "关闭现有 WebSocket 连接")
            webSocket?.close(1000, "重新连接")
            webSocket = null
        }
        
        request?.let { req ->
            okHttpClient?.let { client ->
                isConnecting = true
                webSocket = client.newWebSocket(req, webSocketListener)
            }
        }
    }
    
    /**
     * WebSocket 监听器
     */
    private val webSocketListener = object : WebSocketListener() {
        override fun onOpen(webSocket: WebSocket, response: Response) {
            val connectTime = System.currentTimeMillis()
            Log.i(TAG, "✅ WebSocket 连接成功 - 时间戳: $connectTime")
            Log.i(TAG, "📊 连接信息 - Protocol: ${response.protocol}, Code: ${response.code}")

            isConnecting = false
            reconnectAttempts = 0

            // 开始心跳
            startHeartbeat()

            connectionListener?.onConnected()
        }
        
        override fun onMessage(webSocket: WebSocket, text: String) {
            Log.d(TAG, "📨 收到文本消息: $text")
            connectionListener?.onMessage(text)
        }
        
        override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
            Log.d(TAG, "📨 收到二进制消息: ${bytes.hex()}")
            connectionListener?.onMessage("Binary: ${bytes.hex()}")
        }

        override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
            val failTime = System.currentTimeMillis()
            Log.e(TAG, "❌ WebSocket 连接失败 - 时间戳: $failTime")
            Log.e(TAG, "📊 失败详情 - 异常: ${t.javaClass.simpleName}, 消息: ${t.message}")
            Log.e(TAG, "📊 网络状态 - Response: ${response?.code}, 重连次数: $reconnectAttempts")

            // 记录详细的异常堆栈（仅在调试模式）
            Log.d(TAG, "异常堆栈", t)

            isConnecting = false

            // 停止心跳
            stopHeartbeat()

            val errorMsg = "连接失败: ${t.message}"
            connectionListener?.onError(errorMsg)

            // 根据异常类型调整重连策略
            val reconnectDelay = when {
                t.message?.contains("SocketTimeoutException") == true -> 1000L // 超时快速重连
                t.message?.contains("ConnectException") == true -> 5000L // 连接异常稍慢重连
                else -> RECONNECT_INTERVAL
            }

            // 如果不是手动断开，尝试重连
            if (!isManualDisconnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect(reconnectDelay)
            }
        }
        
        override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
            Log.w(TAG, "🔄 WebSocket 正在关闭: code=$code, reason=$reason")
        }
        
        override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
            val closeTime = System.currentTimeMillis()
            Log.w(TAG, "❌ WebSocket 已关闭 - 时间戳: $closeTime, code=$code, reason=$reason")
            Log.w(TAG, "📊 断开统计 - 手动断开: $isManualDisconnect, 重连次数: $reconnectAttempts")

            isConnecting = false

            // 停止心跳
            stopHeartbeat()

            connectionListener?.onDisconnected("连接关闭: $reason (code: $code)")

            // 根据关闭码调整重连策略
            val reconnectDelay = when (code) {
                1000 -> 2000L // 正常关闭，快速重连
                1001 -> 3000L // 端点离开
                1006 -> 1000L // 异常关闭，立即重连
                else -> RECONNECT_INTERVAL
            }

            // 如果不是手动断开，尝试重连
            if (!isManualDisconnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                scheduleReconnect(reconnectDelay)
            }
        }
    }
    
    /**
     * 发送消息
     */
    fun sendMessage(message: String): Boolean {
        return webSocket?.send(message) ?: false.also {
            Log.w(TAG, "WebSocket 未连接，无法发送消息: $message")
        }
    }
    
    /**
     * 发送心跳
     */
    private fun sendHeartbeat(): Boolean {
        val currentTime = System.currentTimeMillis()
        val heartbeatMsg = """{"type":"heartbeat","timestamp":$currentTime,"screen_off":$isScreenOff}"""

        val success = sendMessage(heartbeatMsg)
        if (success) {
            lastHeartbeatTime = currentTime
            Log.d(TAG, "💓 发送心跳成功 ${if (isScreenOff) "(息屏)" else "(亮屏)"}")
        } else {
            Log.w(TAG, "💔 发送心跳失败")
        }
        return success
    }
    
    /**
     * 开始心跳
     */
    private fun startHeartbeat() {
        stopHeartbeat()
        mainHandler.postDelayed(heartbeatRunnable, HEARTBEAT_INTERVAL)
        Log.d(TAG, "💓 开始心跳检测")
    }
    
    /**
     * 停止心跳
     */
    private fun stopHeartbeat() {
        mainHandler.removeCallbacks(heartbeatRunnable)
        Log.d(TAG, "💔 停止心跳检测")
    }
    
    /**
     * 安排重连
     */
    private fun scheduleReconnect(delay: Long = RECONNECT_INTERVAL) {
        reconnectAttempts++
        Log.i(TAG, "🔄 安排重连，第 $reconnectAttempts 次，${delay}ms 后执行")

        connectionListener?.onReconnecting(reconnectAttempts)

        mainHandler.removeCallbacks(reconnectRunnable)
        mainHandler.postDelayed(reconnectRunnable, delay)
    }

    /**
     * 心跳失败导致的重连
     */
    private fun reconnectDueToHeartbeatFailure() {
        Log.w(TAG, "💔 心跳失败重连")
        stopHeartbeat()

        // 关闭当前连接
        webSocket?.close(1000, "心跳失败")
        webSocket = null

        // 立即重连
        if (!isManualDisconnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
            scheduleReconnect(1000L) // 1秒后重连
        }
    }

    /**
     * 设置息屏状态
     */
    fun setScreenState(isScreenOff: Boolean) {
        this.isScreenOff = isScreenOff
        Log.i(TAG, "📱 屏幕状态变化: ${if (isScreenOff) "息屏" else "亮屏"}")

        // 息屏时立即发送一次心跳，确认连接状态
        if (isScreenOff && isConnected()) {
            sendHeartbeat()
        }
    }
    
    /**
     * 手动断开连接
     */
    fun disconnect() {
        Log.i(TAG, "🔌 手动断开 WebSocket 连接")
        isManualDisconnect = true
        reconnectAttempts = MAX_RECONNECT_ATTEMPTS // 阻止重连
        
        // 停止心跳和重连
        stopHeartbeat()
        mainHandler.removeCallbacks(reconnectRunnable)
        
        // 关闭连接
        webSocket?.close(1000, "手动断开")
        webSocket = null
    }
    
    /**
     * 获取连接状态
     */
    fun isConnected(): Boolean {
        return webSocket != null && !isConnecting
    }
    
    /**
     * 获取连接状态描述
     */
    fun getConnectionStatus(): String {
        val baseStatus = when {
            isConnecting -> "连接中..."
            webSocket != null -> "已连接"
            isManualDisconnect -> "已断开"
            reconnectAttempts > 0 -> "重连中... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})"
            else -> "未连接"
        }

        val additionalInfo = mutableListOf<String>()
        if (isScreenOff) additionalInfo.add("息屏")
        if (heartbeatFailCount > 0) additionalInfo.add("心跳失败${heartbeatFailCount}次")
        if (lastHeartbeatTime > 0) {
            val timeSinceLastHeartbeat = (System.currentTimeMillis() - lastHeartbeatTime) / 1000
            additionalInfo.add("上次心跳${timeSinceLastHeartbeat}秒前")
        }

        return if (additionalInfo.isNotEmpty()) {
            "$baseStatus (${additionalInfo.joinToString(", ")})"
        } else {
            baseStatus
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        Log.i(TAG, "🧹 清理 WebSocket 资源")
        disconnect()
        connectionListener = null
        okHttpClient = null
        request = null
    }
}
