package com.totwoo.totwoo.tcp

import org.junit.Test
import org.junit.Assert.*

/**
 * TCP连接管理器单元测试
 */
class TcpConnectionManagerTest {

    @Test
    fun testSingletonInstance() {
        val instance1 = TcpConnectionManager.getInstance()
        val instance2 = TcpConnectionManager.getInstance()
        
        assertSame("应该返回同一个实例", instance1, instance2)
    }

    @Test
    fun testInitialState() {
        val manager = TcpConnectionManager.getInstance()
        
        assertFalse("初始状态应该是未连接", manager.isConnected())
        assertTrue("初始状态描述应该包含'未连接'", manager.getConnectionStatus().contains("未连接"))
    }
}
