# TCP 长连接管理器实现文档

## 概述

为 ToTwoo Android 应用实现了一个稳定的 TCP 长连接管理器，支持连接到 `34.94.214.133:8089` 服务器，具备 30 秒心跳保活、自动重连、息屏后台保活等功能，并在 DebugActivity 中集成了完整的测试界面。

## 实现的文件

### 1. TcpConnectionManager.kt
**路径**: `totwoo/src/main/java/com/totwoo/totwoo/tcp/TcpConnectionManager.kt`

**主要功能**:
- 单例模式的 TCP 连接管理器
- 支持自动重连（最多10次）
- 30秒心跳间隔保活
- 息屏状态下心跳间隔自动调整为60秒
- 完整的连接状态监听
- 线程安全的连接管理
- 支持发送和接收消息
- 自动处理连接异常和重连

**核心特性**:
- **连接超时**: 10秒
- **读取超时**: 35秒（比心跳间隔长）
- **心跳间隔**: 30秒（息屏时60秒）
- **重连间隔**: 5秒
- **最大重连次数**: 10次
- **心跳消息**: "PING"
- **心跳响应**: "PONG"

### 2. DebugActivity.kt 更新
**路径**: `totwoo/src/main/java/com/totwoo/totwoo/activity/DebugActivity.kt`

**新增功能**:
- TCP 连接管理器集成
- TCP 连接测试按钮
- TCP 消息发送测试
- TCP 连接状态检查
- TCP 后台保活测试
- 息屏状态监听（同时控制 WebSocket 和 TCP）
- 完整的日志记录和 xlog 输出

### 3. 布局文件更新
**路径**: `totwoo/src/main/res/layout/activity_debug.xml`

**新增UI组件**:
- TCP 长连接测试区域标题
- 连接 TCP 服务器按钮
- 断开 TCP 连接按钮
- 发送 TCP 测试消息按钮
- 检查 TCP 连接状态按钮
- 开始 TCP 后台保活测试按钮
- 停止 TCP 后台测试按钮

### 4. 单元测试
**路径**: `totwoo/src/test/java/com/totwoo/totwoo/tcp/TcpConnectionManagerTest.kt`

**测试内容**:
- 单例模式验证
- 初始状态验证

## 使用方法

### 在 DebugActivity 中测试

1. **连接 TCP 服务器**
   - 点击"连接 TCP 服务器"按钮
   - 自动连接到 `34.94.214.133:8089`
   - 查看连接状态日志

2. **发送测试消息**
   - 连接成功后点击"发送 TCP 测试消息"
   - 发送带时间戳的测试消息
   - 查看发送结果

3. **检查连接状态**
   - 点击"检查 TCP 连接状态"
   - 查看详细的连接信息
   - 包括心跳状态、重连次数等

4. **后台保活测试**
   - 点击"开始 TCP 后台保活测试"
   - 息屏或切换到后台
   - 观察连接稳定性
   - 每分钟自动记录连接状态

### 编程接口使用

```kotlin
// 获取实例
val tcpManager = TcpConnectionManager.getInstance()

// 连接服务器
tcpManager.connect("34.94.214.133", 8089, object : TcpConnectionManager.ConnectionListener {
    override fun onConnected() {
        // 连接成功
    }
    
    override fun onDisconnected(reason: String) {
        // 连接断开
    }
    
    override fun onMessage(message: String) {
        // 收到消息
    }
    
    override fun onError(error: String) {
        // 连接错误
    }
    
    override fun onReconnecting(attempt: Int) {
        // 重连中
    }
})

// 发送消息
tcpManager.sendMessage("Hello Server")

// 设置息屏状态
tcpManager.setScreenState(true) // 息屏
tcpManager.setScreenState(false) // 亮屏

// 断开连接
tcpManager.disconnect()

// 清理资源
tcpManager.cleanup()
```

## 技术特点

### 1. 稳定性保障
- 自动重连机制
- 心跳保活检测
- 连接异常处理
- 线程安全设计

### 2. 性能优化
- 息屏时降低心跳频率
- 异步连接和消息处理
- 资源自动清理
- 内存泄漏防护

### 3. 监控和调试
- 完整的 xlog 日志记录
- 连接状态实时监控
- 详细的错误信息
- 可视化测试界面

### 4. 后台保活
- 支持息屏后台运行
- 自动调整心跳策略
- 长时间连接稳定性测试
- 网络状态变化适应

## 日志输出示例

```
[14:30:15] 🔧 TCP 长连接测试模块已初始化
[14:30:15] 📡 目标服务器: 34.94.214.133:8089
[14:30:15] 💓 心跳间隔: 30秒
[14:30:15] 💡 提示: 支持自动重连和息屏后台保活
[14:30:20] 🔄 开始连接 TCP 服务器: 34.94.214.133:8089
[14:30:21] ✅ TCP 连接成功
[14:30:21] 💓 开始心跳检测
[14:30:25] 📤 发送 TCP 测试消息成功: Hello from ToTwoo TCP Test - 1703145025000
[14:30:51] 💓 发送心跳成功 (亮屏)
[14:31:21] 💓 发送心跳成功 (亮屏)
[14:31:30] 📱 屏幕息屏 - 开始测试后台连接稳定性
[14:32:21] 💓 发送心跳成功 (息屏)
```

## 注意事项

1. **网络权限**: 确保应用具有网络访问权限
2. **后台运行**: 需要相应的后台运行权限
3. **资源清理**: Activity 销毁时会自动清理资源
4. **线程安全**: 所有公共方法都是线程安全的
5. **内存管理**: 使用弱引用避免内存泄漏

## 测试建议

1. **基础连接测试**: 验证连接建立和断开
2. **消息收发测试**: 验证消息发送和接收
3. **重连测试**: 模拟网络中断和恢复
4. **长时间测试**: 验证长时间连接稳定性
5. **息屏测试**: 验证后台保活功能
6. **异常处理测试**: 验证各种异常情况的处理

## 扩展建议

1. **消息协议**: 可以扩展支持更复杂的消息协议
2. **加密传输**: 可以添加 SSL/TLS 支持
3. **负载均衡**: 可以支持多服务器连接
4. **消息队列**: 可以添加离线消息缓存
5. **统计监控**: 可以添加连接质量统计
